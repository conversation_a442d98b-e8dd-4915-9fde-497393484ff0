// Handle login form
if (document.getElementById('loginForm')) {
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const loginData = Object.fromEntries(formData);
        
        // Simulate login process
        console.log('Login data:', loginData);
        
        // Show success message and redirect
        alert('تم تسجيل الدخول بنجاح!');
        
        // Redirect based on user type (simulate)
        if (loginData.email.includes('owner')) {
            window.location.href = 'owner-dashboard.html';
        } else if (loginData.email.includes('admin')) {
            window.location.href = 'admin-dashboard.html';
        } else {
            window.location.href = 'index.html';
        }
    });
}

// Handle register form
if (document.getElementById('registerForm')) {
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const registerData = Object.fromEntries(formData);
        
        // Validate password confirmation
        if (registerData.password !== registerData.confirmPassword) {
            alert('كلمة المرور غير متطابقة!');
            return;
        }
        
        // Simulate registration process
        console.log('Register data:', registerData);
        
        // Show success message and redirect
        alert('تم إنشاء الحساب بنجاح!');
        
        // Redirect based on user type
        if (registerData.userType === 'owner') {
            window.location.href = 'owner-dashboard.html';
        } else {
            window.location.href = 'login.html';
        }
    });
}