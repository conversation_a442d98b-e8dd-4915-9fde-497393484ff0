// Sample data for admin dashboard
const adminProperties = [
    {
        id: 1,
        title: "شقة فاخرة في الرياض",
        owner: "أحمد محمد",
        location: "حي الملز، الرياض",
        price: 300,
        status: "معتمدة"
    },
    {
        id: 2,
        title: "شقة مفروشة في جدة",
        owner: "فاطمة علي",
        location: "حي الروضة، جدة",
        price: 250,
        status: "في انتظار الاعتماد"
    }
];

const adminUsers = [
    {
        id: 1,
        name: "أحمد محمد",
        email: "<EMAIL>",
        type: "مالك",
        joinDate: "2024-01-15",
        status: "نشط"
    },
    {
        id: 2,
        name: "فاطمة علي",
        email: "<EMAIL>",
        type: "ضيف",
        joinDate: "2024-01-20",
        status: "نشط"
    }
];

const adminBookings = [
    {
        id: "BK001",
        property: "شقة فاخرة في الرياض",
        guest: "سارة أحمد",
        owner: "أحمد محمد",
        date: "2024-02-15 إلى 2024-02-20",
        amount: 1500,
        status: "مؤكد"
    }
];

const recentActivities = [
    {
        icon: "fas fa-plus text-success",
        text: "تم إضافة شقة جديدة بواسطة أحمد محمد",
        time: "منذ 5 دقائق"
    },
    {
        icon: "fas fa-calendar text-primary",
        text: "حجز جديد من سارة أحمد",
        time: "منذ 15 دقيقة"
    },
    {
        icon: "fas fa-user text-info",
        text: "مستخدم جديد: محمد علي",
        time: "منذ ساعة"
    }
];

function loadAdminProperties() {
    const tableBody = document.getElementById('adminPropertiesTable');
    tableBody.innerHTML = '';
    
    adminProperties.forEach(property => {
        const statusClass = property.status === 'معتمدة' ? 'success' : 'warning';
        const row = `
            <tr>
                <td>${property.id}</td>
                <td>${property.title}</td>
                <td>${property.owner}</td>
                <td>${property.location}</td>
                <td>${property.price} ر.س/ليلة</td>
                <td><span class="badge bg-${statusClass}">${property.status}</span></td>
                <td>
                    ${property.status === 'في انتظار الاعتماد' ? 
                        `<button class="btn btn-sm btn-success" onclick="approveProperty(${property.id})">
                            <i class="fas fa-check"></i> اعتماد
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="rejectProperty(${property.id})">
                            <i class="fas fa-times"></i> رفض
                        </button>` :
                        `<button class="btn btn-sm btn-warning" onclick="suspendProperty(${property.id})">
                            <i class="fas fa-pause"></i> إيقاف
                        </button>`
                    }
                    <button class="btn btn-sm btn-info" onclick="viewPropertyDetails(${property.id})">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

function loadAdminUsers() {
    const tableBody = document.getElementById('adminUsersTable');
    tableBody.innerHTML = '';
    
    adminUsers.forEach(user => {
        const statusClass = user.status === 'نشط' ? 'success' : 'danger';
        const row = `
            <tr>
                <td>${user.id}</td>
                <td>${user.name}</td>
                <td>${user.email}</td>
                <td>${user.type}</td>
                <td>${user.joinDate}</td>
                <td><span class="badge bg-${statusClass}">${user.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-warning" onclick="suspendUser(${user.id})">
                        <i class="fas fa-ban"></i> إيقاف
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewUserDetails(${user.id})">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

function loadAdminBookings() {
    const tableBody = document.getElementById('adminBookingsTable');
    tableBody.innerHTML = '';
    
    adminBookings.forEach(booking => {
        const statusClass = booking.status === 'مؤكد' ? 'success' : 'warning';
        const row = `
            <tr>
                <td>${booking.id}</td>
                <td>${booking.property}</td>
                <td>${booking.guest}</td>
                <td>${booking.owner}</td>
                <td>${booking.date}</td>
                <td>${booking.amount} ر.س</td>
                <td><span class="badge bg-${statusClass}">${booking.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewBookingDetails('${booking.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="cancelBooking('${booking.id}')">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

function loadRecentActivities() {
    const container = document.getElementById('recentActivities');
    container.innerHTML = '';
    
    recentActivities.forEach(activity => {
        const activityItem = `
            <div class="list-group-item d-flex justify-content-between align-items-start">
                <div class="ms-2 me-auto">
                    <div class="fw-bold">
                        <i class="${activity.icon}"></i> ${activity.text}
                    </div>
                </div>
                <small class="text-muted">${activity.time}</small>
            </div>
        `;
        container.innerHTML += activityItem;
    });
}

function initCharts() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الإيرادات (ر.س)',
                data: [12000, 19000, 15000, 25000, 22000, 30000],
                borderColor: 'rgb(75, 192, 192)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Bookings Chart
    const bookingsCtx = document.getElementById('bookingsChart').getContext('2d');
    new Chart(bookingsCtx, {
        type: 'bar',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'عدد الحجوزات',
                data: [65, 89, 80, 81, 96, 105],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Admin action functions
function approveProperty(id) {
    alert(`تم اعتماد الشقة رقم ${id}`);
    loadAdminProperties();
}

function rejectProperty(id) {
    if (confirm('هل أنت متأكد من رفض هذه الشقة؟')) {
        alert(`تم رفض الشقة رقم ${id}`);
        loadAdminProperties();
    }
}

function suspendProperty(id) {
    if (confirm('هل أنت متأكد من إيقاف هذه الشقة؟')) {
        alert(`تم إيقاف الشقة رقم ${id}`);
        loadAdminProperties();
    }
}

function viewPropertyDetails(id) {
    alert(`عرض تفاصيل الشقة رقم ${id}`);
}

function suspendUser(id) {
    if (confirm('هل أنت متأكد من إيقاف هذا المستخدم؟')) {
        alert(`تم إيقاف المستخدم رقم ${id}`);
        loadAdminUsers();
    }
}

function viewUserDetails(id) {
    alert(`عرض تفاصيل المستخدم رقم ${id}`);
}

function viewBookingDetails(id) {
    alert(`عرض تفاصيل الحجز ${id}`);
}

function cancelBooking(id) {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) {
        alert(`تم إلغاء الحجز ${id}`);
        loadAdminBookings();
    }
}

function approveAllPending() {
    if (confirm('هل أنت متأكد من اعتماد جميع الشقق المعلقة؟')) {
        alert('تم اعتماد جميع الشقق المعلقة');
        loadAdminProperties();
    }
}

// Handle settings form
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('تم حفظ الإعدادات بنجاح!');
});

// Initialize admin dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadAdminProperties();
    loadAdminUsers();
    loadAdminBookings();
    loadRecentActivities();
    initCharts();
});