// Sample data for owner properties
const ownerProperties = [
    {
        id: 1,
        title: "شقة فاخرة في الرياض",
        location: "حي الملز، الرياض",
        price: 300,
        status: "متاحة",
        image: "https://via.placeholder.com/100x80"
    },
    {
        id: 2,
        title: "شقة مفروشة في جدة",
        location: "حي الروضة، جدة",
        price: 250,
        status: "محجوزة",
        image: "https://via.placeholder.com/100x80"
    }
];

// Sample bookings data
const ownerBookings = [
    {
        id: "BK001",
        property: "شقة فاخرة في الرياض",
        guest: "أحمد محمد",
        date: "2024-02-15 إلى 2024-02-20",
        amount: 1500,
        status: "مؤكد"
    },
    {
        id: "BK002",
        property: "شقة مفروشة في جدة",
        guest: "فاطمة علي",
        date: "2024-02-10 إلى 2024-02-12",
        amount: 500,
        status: "منتهي"
    }
];

function loadOwnerProperties() {
    const tableBody = document.getElementById('propertiesTable');
    tableBody.innerHTML = '';
    
    ownerProperties.forEach(property => {
        const statusClass = property.status === 'متاحة' ? 'success' : 'warning';
        const row = `
            <tr>
                <td><img src="${property.image}" class="img-thumbnail" style="width: 80px; height: 60px; object-fit: cover;"></td>
                <td>${property.title}</td>
                <td>${property.location}</td>
                <td>${property.price} ر.س/ليلة</td>
                <td><span class="badge bg-${statusClass}">${property.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editProperty(${property.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProperty(${property.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

function loadOwnerBookings() {
    const tableBody = document.getElementById('bookingsTable');
    tableBody.innerHTML = '';
    
    ownerBookings.forEach(booking => {
        const statusClass = booking.status === 'مؤكد' ? 'success' : 'secondary';
        const row = `
            <tr>
                <td>${booking.id}</td>
                <td>${booking.property}</td>
                <td>${booking.guest}</td>
                <td>${booking.date}</td>
                <td>${booking.amount} ر.س</td>
                <td><span class="badge bg-${statusClass}">${booking.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-info" onclick="viewBookingDetails('${booking.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    ${booking.status === 'مؤكد' ? 
                        `<button class="btn btn-sm btn-warning" onclick="cancelBooking('${booking.id}')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>` : ''
                    }
                </td>
            </tr>
        `;
        tableBody.innerHTML += row;
    });
}

function showAddPropertyTab() {
    // Switch to add property tab
    const addPropertyTab = document.querySelector('a[href="#add-property"]');
    const addPropertyPane = document.getElementById('add-property');
    
    // Remove active class from all tabs
    document.querySelectorAll('.nav-link').forEach(tab => tab.classList.remove('active'));
    document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('show', 'active'));
    
    // Activate add property tab
    addPropertyTab.classList.add('active');
    addPropertyPane.classList.add('show', 'active');
}

function editProperty(id) {
    alert(`تعديل الشقة رقم ${id}`);
}

function deleteProperty(id) {
    if (confirm('هل أنت متأكد من حذف هذه الشقة؟')) {
        alert(`تم حذف الشقة رقم ${id}`);
    }
}

function viewBookingDetails(id) {
    alert(`عرض تفاصيل الحجز ${id}`);
}

function cancelBooking(id) {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) {
        alert(`تم إلغاء الحجز ${id}`);
    }
}

// Handle add property form
document.getElementById('addPropertyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const propertyData = Object.fromEntries(formData);
    
    console.log('New property data:', propertyData);
    alert('تم إضافة الشقة بنجاح!');
    
    // Reset form
    this.reset();
    
    // Switch back to properties tab
    document.querySelector('a[href="#properties"]').click();
});

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadOwnerProperties();
    loadOwnerBookings();
});