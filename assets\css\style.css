body {
    font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 100px 0;
    color: white;
}

.search-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    color: #333;
}

.property-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    margin-bottom: 30px;
}

.property-card:hover {
    transform: translateY(-5px);
}

.property-image {
    height: 200px;
    object-fit: cover;
}

.price-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #28a745;
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-weight: bold;
}

.rating {
    color: #ffc107;
}

.sidebar {
    min-height: calc(100vh - 56px);
}

.contact-info .contact-item {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.contact-info .contact-item:last-child {
    border-bottom: none;
}

.contact-icon {
    width: 60px;
    text-align: center;
}

.stat-item {
    padding: 20px;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.map-container {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 10px;
    overflow: hidden;
}

.social-links a {
    margin: 0 5px;
}

/* Admin Dashboard Styles */
.admin-card {
    border-left: 4px solid #007bff;
}

.admin-card.success {
    border-left-color: #28a745;
}

.admin-card.warning {
    border-left-color: #ffc107;
}

.admin-card.danger {
    border-left-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 50px 0;
    }
    
    .search-card {
        padding: 20px;
    }
    
    .sidebar {
        min-height: auto;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate

