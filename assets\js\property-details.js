// Sample property data
const propertyData = {
    1: {
        title: "شقة فاخرة في الرياض",
        location: "حي الملز، الرياض",
        price: 300,
        rooms: 2,
        rating: 4.5,
        images: [
            "https://via.placeholder.com/800x400",
            "https://via.placeholder.com/800x400",
            "https://via.placeholder.com/800x400"
        ],
        amenities: ["واي فاي مجاني", "مكيف", "مطبخ مجهز", "موقف سيارة", "مسبح"],
        description: "شقة فاخرة مفروشة بالكامل في موقع مميز بحي الملز. تتميز بتصميم عصري وإطلالة رائعة على المدينة.",
        owner: {
            phone: "+966501234567",
            email: "<EMAIL>"
        },
        reviews: [
            {
                name: "أحمد محمد",
                rating: 5,
                comment: "شقة رائعة ونظيفة، الموقع ممتاز والخدمة ممتازة",
                date: "2024-01-15"
            },
            {
                name: "فاطمة علي",
                rating: 4,
                comment: "شقة جميلة ومريحة، أنصح بها",
                date: "2024-01-10"
            }
        ]
    }
};

function loadPropertyDetails() {
    const urlParams = new URLSearchParams(window.location.search);
    const propertyId = urlParams.get('id');
    const property = propertyData[propertyId];
    
    if (!property) {
        document.body.innerHTML = '<div class="container mt-5"><h3>الشقة غير موجودة</h3></div>';
        return;
    }
    
    // Load basic info
    document.getElementById('propertyTitle').textContent = property.title;
    document.getElementById('propertyLocation').innerHTML = `<i class="fas fa-map-marker-alt"></i> ${property.location}`;
    document.getElementById('propertyRooms').textContent = `${property.rooms} غرف`;
    document.getElementById('propertyRating').innerHTML = `${'★'.repeat(Math.floor(property.rating))} ${property.rating}`;
    document.getElementById('propertyPrice').textContent = `${property.price} ر.س/ليلة`;
    document.getElementById('propertyDescription').textContent = property.description;
    document.getElementById('nightlyPrice').textContent = `${property.price} ر.س`;
    document.getElementById('ownerPhone').textContent = property.owner.phone;
    document.getElementById('ownerEmail').textContent = property.owner.email;
    
    // Load images
    const imagesContainer = document.getElementById('propertyImages');
    property.images.forEach((image, index) => {
        const imageDiv = `
            <div class="carousel-item ${index === 0 ? 'active' : ''}">
                <img src="${image}" class="d-block w-100" style="height: 400px; object-fit: cover;" alt="صورة الشقة">
            </div>
        `;
        imagesContainer.innerHTML += imageDiv;
    });
    
    // Load amenities
    const amenitiesContainer = document.getElementById('propertyAmenities');
    property.amenities.forEach(amenity => {
        amenitiesContainer.innerHTML += `<span class="badge bg-light text-dark me-2 mb-2">${amenity}</span>`;
    });
    
    // Load reviews
    const reviewsContainer = document.getElementById('reviewsSection');
    property.reviews.forEach(review => {
        const reviewDiv = `
            <div class="border-bottom pb-3 mb-3">
                <div class="d-flex justify-content-between">
                    <h6>${review.name}</h6>
                    <small class="text-muted">${review.date}</small>
                </div>
                <div class="rating mb-2">${'★'.repeat(review.rating)}</div>
                <p>${review.comment}</p>
            </div>
        `;
        reviewsContainer.innerHTML += reviewDiv;
    });
}

// Calculate total price
function calculateTotal() {
    const checkin = document.querySelector('input[name="checkin"]').value;
    const checkout = document.querySelector('input[name="checkout"]').value;
    
    if (checkin && checkout) {
        const checkinDate = new Date(checkin);
        const checkoutDate = new Date(checkout);
        const timeDiff = checkoutDate.getTime() - checkinDate.getTime();
        const nights = Math.ceil(timeDiff / (1000 * 3600 * 24));
        
        if (nights > 0) {
            const urlParams = new URLSearchParams(window.location.search);
            const propertyId = urlParams.get('id');
            const property = propertyData[propertyId];
            const total = nights * property.price;
            
            document.getElementById('totalNights').textContent = nights;
            document.getElementById('totalPrice').textContent = `${total} ر.س`;
        }
    }
}

// Booking form handler
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const bookingData = Object.fromEntries(formData);
    
    // Add property ID
    const urlParams = new URLSearchParams(window.location.search);
    bookingData.propertyId = urlParams.get('id');
    
    // Redirect to booking confirmation
    const params = new URLSearchParams(bookingData).toString();
    window.location.href = `booking.html?${params}`;
});

// Add event listeners for date changes
document.querySelector('input[name="checkin"]').addEventListener('change', calculateTotal);
document.querySelector('input[name="checkout"]').addEventListener('change', calculateTotal);

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadPropertyDetails();
});