// Property data (same as property-details.js)
const propertyData = {
    1: {
        title: "شقة فاخرة في الرياض",
        location: "حي الملز، الرياض",
        price: 300,
        rooms: 2,
        rating: 4.5
    }
};

function loadBookingSummary() {
    const urlParams = new URLSearchParams(window.location.search);
    const propertyId = urlParams.get('propertyId');
    const checkin = urlParams.get('checkin');
    const checkout = urlParams.get('checkout');
    const guests = urlParams.get('guests');
    
    const property = propertyData[propertyId];
    
    if (!property) {
        document.getElementById('bookingSummary').innerHTML = '<p>خطأ في تحميل بيانات الحجز</p>';
        return;
    }
    
    // Calculate nights and total
    const checkinDate = new Date(checkin);
    const checkoutDate = new Date(checkout);
    const nights = Math.ceil((checkoutDate - checkinDate) / (1000 * 3600 * 24));
    const total = nights * property.price;
    
    const summaryHtml = `
        <h6>${property.title}</h6>
        <p class="text-muted small">${property.location}</p>
        <hr>
        <div class="d-flex justify-content-between mb-2">
            <span>تاريخ الوصول:</span>
            <span>${checkin}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span>تاريخ المغادرة:</span>
            <span>${checkout}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span>عدد الضيوف:</span>
            <span>${guests}</span>
        </div>
        <div class="d-flex justify-content-between mb-2">
            <span>عدد الليالي:</span>
            <span>${nights}</span>
        </div>
        <hr>
        <div class="d-flex justify-content-between fw-bold">
            <span>المجموع:</span>
            <span>${total} ر.س</span>
        </div>
    `;
    
    document.getElementById('bookingSummary').innerHTML = summaryHtml;
}

// Handle booking form submission
document.getElementById('finalBookingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show success message
    const successHtml = `
        <div class="text-center">
            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
            <h3 class="mt-3">تم تأكيد الحجز بنجاح!</h3>
            <p>سيتم التواصل معك قريباً لتأكيد التفاصيل</p>
            <p><strong>رقم الحجز:</strong> #${Math.random().toString(36).substr(2, 9).toUpperCase()}</p>
            <a href="index.html" class="btn btn-primary">العودة للرئيسية</a>
        </div>
    `;
    
    document.querySelector('.container').innerHTML = successHtml;
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadBookingSummary();
});