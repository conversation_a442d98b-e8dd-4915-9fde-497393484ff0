<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الشقة - شقق مفروشة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html"><i class="fas fa-home"></i> شقق مفروشة</a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Property Images -->
            <div class="col-lg-8">
                <div id="propertyCarousel" class="carousel slide mb-4" data-bs-ride="carousel">
                    <div class="carousel-inner" id="propertyImages">
                        <!-- Images will be loaded here -->
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#propertyCarousel" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon"></span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#propertyCarousel" data-bs-slide="next">
                        <span class="carousel-control-next-icon"></span>
                    </button>
                </div>

                <!-- Property Description -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h3 id="propertyTitle">جاري التحميل...</h3>
                        <p class="text-muted mb-3" id="propertyLocation"></p>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <span class="badge bg-primary me-2" id="propertyRooms"></span>
                                <span class="rating" id="propertyRating"></span>
                            </div>
                            <div class="col-md-6 text-end">
                                <h4 class="text-success" id="propertyPrice"></h4>
                            </div>
                        </div>
                        <div id="propertyAmenities" class="mb-3"></div>
                        <p id="propertyDescription"></p>
                    </div>
                </div>

                <!-- Reviews Section -->
                <div class="card">
                    <div class="card-header">
                        <h5>تقييمات الزوار</h5>
                    </div>
                    <div class="card-body" id="reviewsSection">
                        <!-- Reviews will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Booking Sidebar -->
            <div class="col-lg-4">
                <div class="card sticky-top">
                    <div class="card-body">
                        <h5 class="card-title">احجز الآن</h5>
                        <form id="bookingForm">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الوصول</label>
                                <input type="date" class="form-control" name="checkin" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">تاريخ المغادرة</label>
                                <input type="date" class="form-control" name="checkout" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الضيوف</label>
                                <select class="form-select" name="guests" required>
                                    <option value="1">ضيف واحد</option>
                                    <option value="2">ضيفان</option>
                                    <option value="3">3 ضيوف</option>
                                    <option value="4">4 ضيوف</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>السعر لليلة الواحدة:</span>
                                    <span id="nightlyPrice">300 ر.س</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>عدد الليالي:</span>
                                    <span id="totalNights">1</span>
                                </div>
                                <hr>
                                <div class="d-flex justify-content-between fw-bold">
                                    <span>المجموع:</span>
                                    <span id="totalPrice">300 ر.س</span>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-calendar-check"></i> احجز الآن
                            </button>
                        </form>
                        
                        <hr>
                        
                        <!-- Contact Info -->
                        <div class="text-center">
                            <h6>معلومات التواصل</h6>
                            <p class="mb-1"><i class="fas fa-phone"></i> <span id="ownerPhone">+966501234567</span></p>
                            <p><i class="fas fa-envelope"></i> <span id="ownerEmail"><EMAIL></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/property-details.js"></script>
</body>
</html>