// Sample search results data
let searchResults = [
    {
        id: 1,
        title: "شقة فاخرة في الرياض",
        location: "حي الملز، الرياض",
        price: 300,
        rooms: 2,
        rating: 4.5,
        image: "https://via.placeholder.com/400x200",
        amenities: ["واي فاي", "مكيف", "مطبخ مجهز"]
    },
    {
        id: 2,
        title: "شقة مفروشة في جدة",
        location: "حي الروضة، جدة",
        price: 250,
        rooms: 1,
        rating: 4.2,
        image: "https://via.placeholder.com/400x200",
        amenities: ["واي فاي", "مكيف", "موقف سيارة"]
    }
];

function displayResults(results) {
    const container = document.getElementById('searchResults');
    const countElement = document.getElementById('resultsCount');
    
    countElement.textContent = `تم العثور على ${results.length} شقة`;
    container.innerHTML = '';
    
    results.forEach(property => {
        const amenitiesHtml = property.amenities.map(amenity => 
            `<span class="badge bg-light text-dark me-1">${amenity}</span>`
        ).join('');
        
        const propertyCard = `
            <div class="col-12 mb-4">
                <div class="card property-card">
                    <div class="row g-0">
                        <div class="col-md-4">
                            <div class="position-relative">
                                <img src="${property.image}" class="img-fluid property-image w-100" alt="${property.title}">
                                <span class="price-badge">${property.price} ر.س/ليلة</span>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="card-body">
                                <h5 class="card-title">${property.title}</h5>
                                <p class="card-text">
                                    <i class="fas fa-map-marker-alt text-primary"></i> ${property.location}
                                </p>
                                <div class="mb-2">
                                    <span class="rating">
                                        ${'★'.repeat(Math.floor(property.rating))} ${property.rating}
                                    </span>
                                    <span class="ms-3">
                                        <i class="fas fa-bed"></i> ${property.rooms} غرف
                                    </span>
                                </div>
                                <div class="mb-3">
                                    ${amenitiesHtml}
                                </div>
                                <button class="btn btn-success" onclick="bookNow(${property.id})">
                                    احجز الآن
                                </button>
                                <button class="btn btn-outline-primary ms-2" onclick="viewProperty(${property.id})">
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        container.innerHTML += propertyCard;
    });
}

function applyFilters() {
    const priceMin = document.getElementById('priceMin').value;
    const priceMax = document.getElementById('priceMax').value;
    const rating = document.getElementById('ratingFilter').value;
    
    let filtered = searchResults;
    
    if (priceMin) {
        filtered = filtered.filter(p => p.price >= parseInt(priceMin));
    }
    if (priceMax) {
        filtered = filtered.filter(p => p.price <= parseInt(priceMax));
    }
    if (rating) {
        filtered = filtered.filter(p => p.rating >= parseFloat(rating));
    }
    
    displayResults(filtered);
}

function bookNow(id) {
    window.location.href = `booking.html?id=${id}`;
}

function viewProperty(id) {
    window.location.href = `property-details.html?id=${id}`;
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    displayResults(searchResults);
});
